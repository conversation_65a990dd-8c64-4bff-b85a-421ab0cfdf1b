const mongoose = require('mongoose');
const User = require('./src/models/User');
const notificationService = require('./src/services/notificationService');
const sessionNotificationService = require('./src/services/sessionNotificationService');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

async function testNotificationSystem() {
  try {
    console.log('🚀 Starting notification system test...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Test 1: Find users with FCM tokens
    console.log('\n📊 Test 1: Checking users with FCM tokens...');
    const usersWithTokens = await User.find({
      'fcmTokens': { $exists: true, $not: { $size: 0 } }
    });
    console.log(`Found ${usersWithTokens.length} users with FCM tokens`);

    if (usersWithTokens.length > 0) {
      const testUser = usersWithTokens[0];
      console.log(`Test user: ${testUser.firebaseUid}`);
      console.log(`FCM tokens: ${testUser.fcmTokens.length}`);
      
      // Test 2: Send a test notification
      console.log('\n📱 Test 2: Sending test notification...');
      const activeToken = testUser.fcmTokens.find(token => token.isActive);
      
      if (activeToken) {
        const result = await notificationService.sendNotificationToUser(
          activeToken.token,
          'ZeroKoin Test',
          'This is a test notification to verify the push notification system is working!'
        );
        
        console.log('Test notification result:', result);
      } else {
        console.log('⚠️ No active FCM tokens found for test user');
      }
    }

    // Test 3: Check for expired sessions
    console.log('\n⏰ Test 3: Checking for expired sessions...');
    const now = new Date();
    const usersWithExpiredSessions = await User.find({
      'sessions': {
        $elemMatch: {
          isLocked: true,
          nextUnlockAt: { $lte: now }
        }
      }
    });
    
    console.log(`Found ${usersWithExpiredSessions.length} users with expired sessions`);

    // Test 4: Manually trigger session notification check
    console.log('\n🔍 Test 4: Triggering manual session check...');
    await sessionNotificationService.triggerCheck();

    // Test 5: Create a test session that expires soon (for testing)
    if (usersWithTokens.length > 0) {
      console.log('\n⚡ Test 5: Creating test session that expires in 10 seconds...');
      const testUser = usersWithTokens[0];
      
      // Find or create a session to test with
      let testSession = testUser.sessions.find(s => s.sessionNumber === 2);
      if (!testSession) {
        testUser.sessions.push({
          sessionNumber: 2,
          isLocked: true,
          nextUnlockAt: new Date(Date.now() + 10000), // 10 seconds from now
          completedAt: null,
          isClaimed: false,
          unlockedAt: null
        });
      } else {
        testSession.isLocked = true;
        testSession.nextUnlockAt = new Date(Date.now() + 10000); // 10 seconds from now
        testSession.completedAt = null;
        testSession.isClaimed = false;
        testSession.unlockedAt = null;
      }
      
      await testUser.save();
      console.log('✅ Test session created - will expire in 10 seconds');
      console.log('💡 You can now wait 10 seconds and check if you receive a push notification');
    }

    console.log('\n✅ Notification system test completed!');
    console.log('\n📋 Summary:');
    console.log(`- Users with FCM tokens: ${usersWithTokens.length}`);
    console.log(`- Users with expired sessions: ${usersWithExpiredSessions.length}`);
    console.log('- Test notification sent (if user had active token)');
    console.log('- Manual session check triggered');
    console.log('- Test session created (expires in 10 seconds)');
    
    console.log('\n🔧 To test the complete flow:');
    console.log('1. Make sure your app is completely closed');
    console.log('2. Wait 10 seconds for the test session to expire');
    console.log('3. The background service should detect the expired session and send a push notification');
    console.log('4. You should receive a notification even with the app closed');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the test
testNotificationSystem();
