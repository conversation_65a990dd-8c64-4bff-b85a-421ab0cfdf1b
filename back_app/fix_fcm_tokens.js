const mongoose = require('mongoose');
const User = require('./src/models/User');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

async function fixFCMTokens() {
  try {
    console.log('🔧 Fixing FCM tokens...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find users with FCM tokens
    const usersWithTokens = await User.find({
      'fcmTokens': { $exists: true, $not: { $size: 0 } }
    });

    console.log(`Found ${usersWithTokens.length} users with FCM tokens`);

    for (const user of usersWithTokens) {
      console.log(`\n👤 User: ${user.firebaseUid}`);
      console.log(`FCM tokens: ${user.fcmTokens.length}`);
      
      let updated = false;
      for (const token of user.fcmTokens) {
        console.log(`Token: ${token.token.substring(0, 20)}... - Active: ${token.isActive}`);
        
        if (!token.isActive) {
          console.log('🔄 Activating token...');
          token.isActive = true;
          token.lastUsed = new Date();
          updated = true;
        }
      }
      
      if (updated) {
        await user.save();
        console.log('✅ User tokens updated');
      } else {
        console.log('ℹ️ No updates needed');
      }
    }

    console.log('\n✅ FCM token fix completed!');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the fix
fixFCMTokens();
